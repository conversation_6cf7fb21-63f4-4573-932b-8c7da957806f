load("@bazel_skylib//rules:common_settings.bzl", "bool_flag")
load("@io_bazel_rules_go//go:def.bzl", "nogo")
load("//build/linter/staticcheck:def.bzl", "staticcheck_analyzers")

package(default_visibility = ["//visibility:public"])

bool_flag(
    name = "with_nogo_flag",
    build_setting_default = False,
    visibility = ["//visibility:public"],
)

config_setting(
    name = "with_nogo",
    flag_values = {
        ":with_nogo_flag": "true",
    },
    visibility = ["//visibility:public"],
)

bool_flag(
    name = "with_rbe_flag",
    build_setting_default = False,
    visibility = ["//visibility:public"],
)

config_setting(
    name = "without_rbe",
    flag_values = {
        ":with_rbe_flag": "false",
    },
    visibility = ["//visibility:public"],
)

STATICHECK_ANALYZERS = [
    "S1000",
    "S1001",
    "S1002",
    "S1003",
    "S1004",
    "S1005",
    "S1006",
    "S1007",
    "S1008",
    "S1009",
    "S1010",
    "S1011",
    "S1012",
    "S1016",
    "S1017",
    "S1018",
    "S1019",
    "S1020",
    "S1021",
    "S1023",
    "S1024",
    "S1025",
    "S1028",
    "S1029",
    "S1030",
    "S1031",
    "S1032",
    "S1033",
    "S1034",
    "S1035",
    "S1036",
    "S1037",
    "S1038",
    "S1039",
    "S1040",
    "SA1019",
    "SA1029",
    "SA2000",
    "SA2001",
    "SA2003",
    "SA3000",
    "SA3001",
    "SA4004",
    "SA4009",
    "SA4018",
    "SA5000",
    "SA5001",
    "SA5002",
    "SA5003",
    "SA5004",
    "SA5005",
    "SA5007",
    "SA5008",
    "SA5009",
    "SA5010",
    #"SA5011",
    "SA5012",
    "SA6000",
    "SA6001",
    "SA6002",
    "SA6005",
    "QF1002",
    "QF1004",
    "QF1012",
    "U1000",
]

nogo(
    name = "tidb_nogo",
    config = ":nogo_config.json",
    visibility = ["//visibility:public"],  # must have public visibility
    deps = [
               "@org_golang_x_tools//go/analysis/passes/asmdecl:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/assign:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/atomic:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/atomicalign:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/bools:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/buildssa:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/buildtag:go_default_library",
               # https://github.com/bazelbuild/rules_go/issues/2396
               # "@org_golang_x_tools//go/analysis/passes/cgocall:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/composite:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/copylock:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/ctrlflow:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/deepequalerrors:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/errorsas:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/fieldalignment:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/findcall:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/httpresponse:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/ifaceassert:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/inspect:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/loopclosure:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/lostcancel:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/nilfunc:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/nilness:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/pkgfact:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/printf:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/shift:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/sortslice:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/stdmethods:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/stringintconv:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/structtag:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/testinggoroutine:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/tests:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/timeformat:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/unmarshal:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/unreachable:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/unsafeptr:go_default_library",
               "@org_golang_x_tools//go/analysis/passes/unusedresult:go_default_library",
               "//build/linter/asciicheck",
               "//build/linter/bodyclose",
               "//build/linter/bootstrap",
               "//build/linter/constructor",
               "//build/linter/deferrecover",
               "//build/linter/durationcheck",
               "//build/linter/etcdconfig",
               "//build/linter/exportloopref",
               "//build/linter/forcetypeassert",
               "//build/linter/gofmt",
               "//build/linter/gci",
               "//build/linter/gosec",
               "//build/linter/ineffassign",
               "//build/linter/makezero",
               "//build/linter/mirror",
               "//build/linter/misspell",
               "//build/linter/prealloc",
               "//build/linter/predeclared",
               "//build/linter/unconvert",
               "//build/linter/rowserrcheck",
               "//build/linter/toomanytests",
           ] + staticcheck_analyzers(STATICHECK_ANALYZERS) +
           select({
               "//build:with_nogo": [
                   "//build/linter/allrevive",
                   "//build/linter/errcheck",
                   "//build/linter/lll",
                   "//build/linter/revive",
               ],
               "//conditions:default": [],
           }) +
           select({
               "//build:without_rbe": [
               ],
               "//conditions:default": [],
           }),
)
