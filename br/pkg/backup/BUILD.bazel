load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "backup",
    srcs = [
        "check.go",
        "client.go",
        "metrics.go",
        "push.go",
        "schema.go",
    ],
    importpath = "github.com/pingcap/tidb/br/pkg/backup",
    visibility = ["//visibility:public"],
    deps = [
        "//br/pkg/checkpoint",
        "//br/pkg/checksum",
        "//br/pkg/conn",
        "//br/pkg/conn/util",
        "//br/pkg/errors",
        "//br/pkg/glue",
        "//br/pkg/logutil",
        "//br/pkg/metautil",
        "//br/pkg/redact",
        "//br/pkg/rtree",
        "//br/pkg/storage",
        "//br/pkg/summary",
        "//br/pkg/utils",
        "//br/pkg/version",
        "//pkg/ddl",
        "//pkg/distsql",
        "//pkg/kv",
        "//pkg/meta",
        "//pkg/parser/model",
        "//pkg/statistics/handle",
        "//pkg/statistics/handle/util",
        "//pkg/util",
        "//pkg/util/codec",
        "//pkg/util/mathutil",
        "//pkg/util/ranger",
        "//pkg/util/table-filter",
        "@com_github_google_btree//:btree",
        "@com_github_opentracing_opentracing_go//:opentracing-go",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_pingcap_kvproto//pkg/brpb",
        "@com_github_pingcap_kvproto//pkg/errorpb",
        "@com_github_pingcap_kvproto//pkg/kvrpcpb",
        "@com_github_pingcap_kvproto//pkg/metapb",
        "@com_github_pingcap_log//:log",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_tikv_client_go_v2//oracle",
        "@com_github_tikv_client_go_v2//tikv",
        "@com_github_tikv_client_go_v2//txnkv/txnlock",
        "@com_github_tikv_client_go_v2//util",
        "@com_github_tikv_pd_client//:client",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_x_sync//errgroup",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "backup_test",
    timeout = "short",
    srcs = [
        "client_test.go",
        "main_test.go",
        "schema_test.go",
        "store_test.go",
    ],
    embed = [":backup"],
    flaky = True,
    race = "on",
    shard_count = 13,
    deps = [
        "//br/pkg/conn",
        "//br/pkg/gluetidb",
        "//br/pkg/metautil",
        "//br/pkg/mock",
        "//br/pkg/pdutil",
        "//br/pkg/storage",
        "//br/pkg/utils",
        "//pkg/kv",
        "//pkg/parser/model",
        "//pkg/sessionctx/variable",
        "//pkg/tablecodec",
        "//pkg/testkit",
        "//pkg/testkit/testsetup",
        "//pkg/types",
        "//pkg/util/codec",
        "//pkg/util/table-filter",
        "@com_github_golang_protobuf//proto",
        "@com_github_pingcap_kvproto//pkg/brpb",
        "@com_github_pingcap_kvproto//pkg/encryptionpb",
        "@com_github_pingcap_kvproto//pkg/errorpb",
        "@com_github_stretchr_testify//require",
        "@com_github_tikv_client_go_v2//oracle",
        "@com_github_tikv_client_go_v2//testutils",
        "@com_github_tikv_client_go_v2//tikv",
        "@com_github_tikv_client_go_v2//txnkv/txnlock",
        "@com_github_tikv_pd_client//:client",
        "@io_opencensus_go//stats/view",
        "@org_golang_google_grpc//:grpc",
        "@org_uber_go_goleak//:goleak",
    ],
)
