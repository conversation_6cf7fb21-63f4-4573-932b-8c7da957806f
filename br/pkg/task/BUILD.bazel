load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "task",
    srcs = [
        "backup.go",
        "backup_ebs.go",
        "backup_raw.go",
        "backup_txn.go",
        "common.go",
        "restore.go",
        "restore_data.go",
        "restore_ebs_meta.go",
        "restore_raw.go",
        "restore_txn.go",
        "stream.go",
    ],
    importpath = "github.com/pingcap/tidb/br/pkg/task",
    visibility = ["//visibility:public"],
    deps = [
        "//br/pkg/aws",
        "//br/pkg/backup",
        "//br/pkg/backup/prepare_snap",
        "//br/pkg/checkpoint",
        "//br/pkg/checksum",
        "//br/pkg/common",
        "//br/pkg/config",
        "//br/pkg/conn",
        "//br/pkg/conn/util",
        "//br/pkg/errors",
        "//br/pkg/glue",
        "//br/pkg/httputil",
        "//br/pkg/logutil",
        "//br/pkg/metautil",
        "//br/pkg/pdutil",
        "//br/pkg/restore",
        "//br/pkg/restore/tiflashrec",
        "//br/pkg/rtree",
        "//br/pkg/storage",
        "//br/pkg/stream",
        "//br/pkg/streamhelper",
        "//br/pkg/streamhelper/config",
        "//br/pkg/streamhelper/daemon",
        "//br/pkg/summary",
        "//br/pkg/utils",
        "//br/pkg/version",
        "//pkg/config",
        "//pkg/infoschema",
        "//pkg/keyspace",
        "//pkg/kv",
        "//pkg/parser/model",
        "//pkg/parser/mysql",
        "//pkg/sessionctx/stmtctx",
        "//pkg/sessionctx/variable",
        "//pkg/statistics/handle",
        "//pkg/types",
        "//pkg/util",
        "//pkg/util/etcd",
        "//pkg/util/mathutil",
        "//pkg/util/sqlexec",
        "//pkg/util/table-filter",
        "@com_github_docker_go_units//:go-units",
        "@com_github_fatih_color//:color",
        "@com_github_gogo_protobuf//proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_opentracing_opentracing_go//:opentracing-go",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_pingcap_kvproto//pkg/brpb",
        "@com_github_pingcap_kvproto//pkg/encryptionpb",
        "@com_github_pingcap_kvproto//pkg/kvrpcpb",
        "@com_github_pingcap_kvproto//pkg/metapb",
        "@com_github_pingcap_log//:log",
        "@com_github_spf13_cobra//:cobra",
        "@com_github_spf13_pflag//:pflag",
        "@com_github_tikv_client_go_v2//config",
        "@com_github_tikv_client_go_v2//oracle",
        "@com_github_tikv_client_go_v2//tikv",
        "@com_github_tikv_client_go_v2//util",
        "@com_github_tikv_pd_client//:client",
        "@com_github_tikv_pd_client//pkg/caller",
        "@com_google_cloud_go_storage//:storage",
        "@io_etcd_go_etcd_client_pkg_v3//transport",
        "@io_etcd_go_etcd_client_v3//:client",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//backoff",
        "@org_golang_google_grpc//keepalive",
        "@org_golang_x_sync//errgroup",
        "@org_uber_go_multierr//:multierr",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "task_test",
    timeout = "short",
    srcs = [
        "backup_ebs_test.go",
        "backup_test.go",
        "common_test.go",
        "restore_test.go",
        "stream_test.go",
    ],
    embed = [":task"],
    flaky = True,
    shard_count = 21,
    deps = [
        "//br/pkg/conn",
        "//br/pkg/errors",
        "//br/pkg/metautil",
        "//br/pkg/restore",
        "//br/pkg/storage",
        "//br/pkg/stream",
        "//br/pkg/utils",
        "//pkg/config",
        "//pkg/parser/model",
        "//pkg/statistics/handle/util",
        "//pkg/tablecodec",
        "//pkg/util/table-filter",
        "@com_github_golang_protobuf//proto",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_kvproto//pkg/brpb",
        "@com_github_pingcap_kvproto//pkg/encryptionpb",
        "@com_github_pingcap_kvproto//pkg/metapb",
        "@com_github_spf13_pflag//:pflag",
        "@com_github_stretchr_testify//require",
        "@com_github_tikv_client_go_v2//oracle",
        "@com_github_tikv_pd_client//:client",
        "@com_github_tikv_pd_client//opt",
        "@org_golang_google_grpc//keepalive",
    ],
)
