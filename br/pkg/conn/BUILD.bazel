load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "conn",
    srcs = ["conn.go"],
    importpath = "github.com/pingcap/tidb/br/pkg/conn",
    visibility = ["//visibility:public"],
    deps = [
        "//br/pkg/conn/util",
        "//br/pkg/errors",
        "//br/pkg/glue",
        "//br/pkg/logutil",
        "//br/pkg/pdutil",
        "//br/pkg/utils",
        "//br/pkg/version",
        "//pkg/config",
        "//pkg/domain",
        "//pkg/kv",
        "@com_github_docker_go_units//:go-units",
        "@com_github_opentracing_opentracing_go//:opentracing-go",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_pingcap_kvproto//pkg/brpb",
        "@com_github_pingcap_kvproto//pkg/logbackuppb",
        "@com_github_pingcap_kvproto//pkg/metapb",
        "@com_github_pingcap_log//:log",
        "@com_github_tikv_client_go_v2//oracle",
        "@com_github_tikv_client_go_v2//tikv",
        "@com_github_tikv_client_go_v2//txnkv/txnlock",
        "@com_github_tikv_pd_client//:client",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//keepalive",
        "@org_golang_google_grpc//status",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "conn_test",
    timeout = "short",
    srcs = [
        "conn_test.go",
        "main_test.go",
    ],
    embed = [":conn"],
    flaky = True,
    shard_count = 8,
    deps = [
        "//br/pkg/conn/util",
        "//br/pkg/pdutil",
        "//br/pkg/utils",
        "//pkg/testkit/testsetup",
        "@com_github_docker_go_units//:go-units",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_pingcap_kvproto//pkg/metapb",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_uber_go_goleak//:goleak",
        "@org_uber_go_multierr//:multierr",
    ],
)
