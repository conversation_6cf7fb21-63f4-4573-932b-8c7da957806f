load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "utils",
    srcs = [
        "backoff.go",
        "cdc.go",
        "db.go",
        "debug.go",
        "dyn_pprof_other.go",
        "dyn_pprof_unix.go",
        "env.go",
        "json.go",
        "key.go",
        "math.go",
        "misc.go",
        "permission.go",
        "pointer.go",
        "pprof.go",
        "progress.go",
        "register.go",
        "retry.go",
        "safe_point.go",
        "schema.go",
        "sensitive.go",
        "store_manager.go",
        "wait.go",
        "worker.go",
    ],
    importpath = "github.com/pingcap/tidb/br/pkg/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//br/pkg/errors",
        "//br/pkg/logutil",
        "//br/pkg/metautil",
        "//br/pkg/storage",
        "//pkg/config",
        "//pkg/errno",
        "//pkg/keyspace",
        "//pkg/kv",
        "//pkg/parser/model",
        "//pkg/parser/mysql",
        "//pkg/parser/terror",
        "//pkg/parser/types",
        "//pkg/sessionctx",
        "//pkg/util",
        "//pkg/util/mathutil",
        "//pkg/util/sqlexec",
        "@com_github_cheggaaa_pb_v3//:pb",
        "@com_github_docker_go_units//:go-units",
        "@com_github_gogo_protobuf//proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_pingcap_kvproto//pkg/brpb",
        "@com_github_pingcap_kvproto//pkg/keyspacepb",
        "@com_github_pingcap_kvproto//pkg/metapb",
        "@com_github_pingcap_kvproto//pkg/pdpb",
        "@com_github_pingcap_log//:log",
        "@com_github_tikv_client_go_v2//oracle",
        "@com_github_tikv_client_go_v2//tikv",
        "@com_github_tikv_pd_client//:client",
        "@com_github_tikv_pd_client//opt",
        "@io_etcd_go_etcd_client_v3//:client",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//backoff",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_grpc//keepalive",
        "@org_golang_google_grpc//status",
        "@org_golang_x_net//http/httpproxy",
        "@org_golang_x_sync//errgroup",
        "@org_uber_go_atomic//:atomic",
        "@org_uber_go_multierr//:multierr",
        "@org_uber_go_zap//:zap",
        "@org_uber_go_zap//zapcore",
    ],
)

go_test(
    name = "utils_test",
    timeout = "short",
    srcs = [
        "backoff_test.go",
        "cdc_test.go",
        "db_test.go",
        "debug_test.go",
        "env_test.go",
        "json_test.go",
        "key_test.go",
        "main_test.go",
        "math_test.go",
        "misc_test.go",
        "progress_test.go",
        "register_test.go",
        "retry_test.go",
        "safe_point_test.go",
        "schema_test.go",
        "sensitive_test.go",
    ],
    embed = [":utils"],
    flaky = True,
    shard_count = 39,
    deps = [
        "//br/pkg/errors",
        "//br/pkg/metautil",
        "//br/pkg/storage",
        "//pkg/kv",
        "//pkg/parser/ast",
        "//pkg/parser/model",
        "//pkg/parser/mysql",
        "//pkg/parser/types",
        "//pkg/statistics/handle/util",
        "//pkg/tablecodec",
        "//pkg/testkit/testsetup",
        "//pkg/types",
        "//pkg/util/chunk",
        "//pkg/util/sqlexec",
        "@com_github_gogo_protobuf//proto",
        "@com_github_golang_protobuf//proto",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_pingcap_kvproto//pkg/brpb",
        "@com_github_pingcap_kvproto//pkg/encryptionpb",
        "@com_github_pingcap_kvproto//pkg/errorpb",
        "@com_github_stretchr_testify//require",
        "@com_github_tikv_client_go_v2//tikv",
        "@com_github_tikv_pd_client//:client",
        "@io_etcd_go_etcd_client_v3//:client",
        "@io_etcd_go_etcd_tests_v3//integration",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_uber_go_goleak//:goleak",
        "@org_uber_go_multierr//:multierr",
    ],
)
