load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "common",
    srcs = [
        "common.go",
        "conn.go",
        "dupdetect.go",
        "engine.go",
        "errors.go",
        "ingest_data.go",
        "key_adapter.go",
        "once_error.go",
        "pause.go",
        "retry.go",
        "security.go",
        "storage.go",
        "storage_unix.go",
        "storage_windows.go",
        "util.go",
    ],
    importpath = "github.com/pingcap/tidb/br/pkg/lightning/common",
    visibility = ["//visibility:public"],
    deps = [
        "//br/pkg/errors",
        "//br/pkg/httputil",
        "//br/pkg/lightning/log",
        "//br/pkg/logutil",
        "//br/pkg/utils",
        "//pkg/errno",
        "//pkg/meta/autoid",
        "//pkg/parser/model",
        "//pkg/parser/mysql",
        "//pkg/sessionctx/variable",
        "//pkg/store/driver/error",
        "//pkg/table/tables",
        "//pkg/types",
        "//pkg/util",
        "//pkg/util/codec",
        "//pkg/util/format",
        "@com_github_cockroachdb_pebble//:pebble",
        "@com_github_go_sql_driver_mysql//:mysql",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_tikv_client_go_v2//config",
        "@com_github_tikv_pd_client//:client",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_grpc//status",
        "@org_uber_go_zap//:zap",
    ] + select({
        "@io_bazel_rules_go//go/platform:aix": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:android": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:darwin": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:dragonfly": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:freebsd": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:illumos": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:ios": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:js": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:linux": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:netbsd": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:openbsd": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:osx": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:plan9": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:qnx": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:solaris": [
            "@org_golang_x_sys//unix",
        ],
        "//conditions:default": [],
    }),
)

go_test(
    name = "common_test",
    timeout = "short",
    srcs = [
        "common_test.go",
        "errors_test.go",
        "key_adapter_test.go",
        "main_test.go",
        "once_error_test.go",
        "pause_test.go",
        "retry_test.go",
        "security_test.go",
        "storage_test.go",
        "util_test.go",
    ],
    embed = [":common"],
    flaky = True,
    shard_count = 30,
    deps = [
        "//br/pkg/errors",
        "//br/pkg/lightning/log",
        "//pkg/autoid_service",
        "//pkg/ddl",
        "//pkg/errno",
        "//pkg/kv",
        "//pkg/meta",
        "//pkg/meta/autoid",
        "//pkg/parser",
        "//pkg/parser/ast",
        "//pkg/parser/model",
        "//pkg/sessionctx/stmtctx",
        "//pkg/store/driver/error",
        "//pkg/store/mockstore",
        "//pkg/testkit/testsetup",
        "//pkg/types",
        "//pkg/util/codec",
        "//pkg/util/dbutil",
        "//pkg/util/mock",
        "@com_github_data_dog_go_sqlmock//:go-sqlmock",
        "@com_github_go_sql_driver_mysql//:mysql",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_x_time//rate",
        "@org_uber_go_goleak//:goleak",
        "@org_uber_go_multierr//:multierr",
    ],
)
