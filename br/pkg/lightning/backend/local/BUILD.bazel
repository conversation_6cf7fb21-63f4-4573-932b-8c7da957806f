load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "local",
    srcs = [
        "checksum.go",
        "compress.go",
        "disk_quota.go",
        "duplicate.go",
        "engine.go",
        "iterator.go",
        "local.go",
        "local_freebsd.go",
        "local_unix.go",
        "local_unix_generic.go",
        "local_windows.go",
        "localhelper.go",
        "region_job.go",
        "tikv_mode.go",
    ],
    importpath = "github.com/pingcap/tidb/br/pkg/lightning/backend/local",
    visibility = ["//visibility:public"],
    deps = [
        "//br/pkg/checksum",
        "//br/pkg/errors",
        "//br/pkg/lightning/backend",
        "//br/pkg/lightning/backend/encode",
        "//br/pkg/lightning/backend/external",
        "//br/pkg/lightning/backend/kv",
        "//br/pkg/lightning/checkpoints",
        "//br/pkg/lightning/common",
        "//br/pkg/lightning/config",
        "//br/pkg/lightning/errormanager",
        "//br/pkg/lightning/log",
        "//br/pkg/lightning/manual",
        "//br/pkg/lightning/metric",
        "//br/pkg/lightning/mydump",
        "//br/pkg/lightning/tikv",
        "//br/pkg/lightning/verification",
        "//br/pkg/logutil",
        "//br/pkg/membuf",
        "//br/pkg/pdutil",
        "//br/pkg/restore/split",
        "//br/pkg/storage",
        "//br/pkg/utils",
        "//br/pkg/version",
        "//pkg/distsql",
        "//pkg/infoschema",
        "//pkg/keyspace",
        "//pkg/kv",
        "//pkg/meta",
        "//pkg/metrics",
        "//pkg/parser/model",
        "//pkg/parser/mysql",
        "//pkg/sessionctx/variable",
        "//pkg/store/pdtypes",
        "//pkg/table",
        "//pkg/tablecodec",
        "//pkg/types",
        "//pkg/util/codec",
        "//pkg/util/compress",
        "//pkg/util/engine",
        "//pkg/util/hack",
        "//pkg/util/intest",
        "//pkg/util/mathutil",
        "//pkg/util/ranger",
        "@com_github_cockroachdb_pebble//:pebble",
        "@com_github_cockroachdb_pebble//sstable",
        "@com_github_cockroachdb_pebble//vfs",
        "@com_github_coreos_go_semver//semver",
        "@com_github_docker_go_units//:go-units",
        "@com_github_google_btree//:btree",
        "@com_github_google_uuid//:uuid",
        "@com_github_klauspost_compress//gzip",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_pingcap_kvproto//pkg/errorpb",
        "@com_github_pingcap_kvproto//pkg/import_sstpb",
        "@com_github_pingcap_kvproto//pkg/kvrpcpb",
        "@com_github_pingcap_kvproto//pkg/metapb",
        "@com_github_pingcap_kvproto//pkg/pdpb",
        "@com_github_pingcap_tipb//go-tipb",
        "@com_github_tikv_client_go_v2//error",
        "@com_github_tikv_client_go_v2//kv",
        "@com_github_tikv_client_go_v2//oracle",
        "@com_github_tikv_client_go_v2//tikv",
        "@com_github_tikv_client_go_v2//util",
        "@com_github_tikv_pd_client//:client",
        "@com_github_tikv_pd_client//errs",
        "@com_github_tikv_pd_client//opt",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//backoff",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_grpc//keepalive",
        "@org_golang_google_grpc//status",
        "@org_golang_x_sync//errgroup",
        "@org_golang_x_time//rate",
        "@org_uber_go_atomic//:atomic",
        "@org_uber_go_multierr//:multierr",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "local_test",
    timeout = "short",
    srcs = [
        "checksum_test.go",
        "compress_test.go",
        "disk_quota_test.go",
        "duplicate_test.go",
        "engine_test.go",
        "iterator_test.go",
        "local_check_test.go",
        "local_test.go",
        "localhelper_test.go",
        "region_job_test.go",
    ],
    embed = [":local"],
    flaky = True,
    race = "on",
    shard_count = 50,
    deps = [
        "//br/pkg/lightning/backend",
        "//br/pkg/lightning/backend/encode",
        "//br/pkg/lightning/backend/external",
        "//br/pkg/lightning/backend/kv",
        "//br/pkg/lightning/checkpoints",
        "//br/pkg/lightning/common",
        "//br/pkg/lightning/config",
        "//br/pkg/lightning/log",
        "//br/pkg/lightning/mydump",
        "//br/pkg/membuf",
        "//br/pkg/mock/mocklocal",
        "//br/pkg/pdutil",
        "//br/pkg/restore/split",
        "//br/pkg/storage",
        "//br/pkg/utils",
        "//pkg/ddl",
        "//pkg/errno",
        "//pkg/keyspace",
        "//pkg/kv",
        "//pkg/parser",
        "//pkg/parser/ast",
        "//pkg/parser/model",
        "//pkg/parser/mysql",
        "//pkg/sessionctx/stmtctx",
        "//pkg/store/pdtypes",
        "//pkg/table/tables",
        "//pkg/tablecodec",
        "//pkg/types",
        "//pkg/util",
        "//pkg/util/codec",
        "//pkg/util/engine",
        "//pkg/util/hack",
        "//pkg/util/mock",
        "@com_github_cockroachdb_pebble//:pebble",
        "@com_github_cockroachdb_pebble//sstable",
        "@com_github_coreos_go_semver//semver",
        "@com_github_data_dog_go_sqlmock//:go-sqlmock",
        "@com_github_docker_go_units//:go-units",
        "@com_github_go_sql_driver_mysql//:mysql",
        "@com_github_google_uuid//:uuid",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_pingcap_kvproto//pkg/errorpb",
        "@com_github_pingcap_kvproto//pkg/import_sstpb",
        "@com_github_pingcap_kvproto//pkg/metapb",
        "@com_github_pingcap_kvproto//pkg/pdpb",
        "@com_github_pingcap_tipb//go-tipb",
        "@com_github_stretchr_testify//require",
        "@com_github_tikv_client_go_v2//oracle",
        "@com_github_tikv_pd_client//:client",
        "@com_github_tikv_pd_client//clients/router",
        "@com_github_tikv_pd_client//errs",
        "@com_github_tikv_pd_client//opt",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//encoding",
        "@org_golang_google_grpc//status",
        "@org_uber_go_atomic//:atomic",
        "@org_uber_go_mock//gomock",
    ],
)
