// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: br/pkg/lightning/checkpoints/checkpointspb/file_checkpoints.proto

package checkpointspb

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type CheckpointsModel struct {
	// key is table_name
	Checkpoints    map[string]*TableCheckpointModel `protobuf:"bytes,1,rep,name=checkpoints,proto3" json:"checkpoints,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TaskCheckpoint *TaskCheckpointModel             `protobuf:"bytes,2,opt,name=task_checkpoint,json=taskCheckpoint,proto3" json:"task_checkpoint,omitempty"`
}

func (m *CheckpointsModel) Reset()         { *m = CheckpointsModel{} }
func (m *CheckpointsModel) String() string { return proto.CompactTextString(m) }
func (*CheckpointsModel) ProtoMessage()    {}
func (*CheckpointsModel) Descriptor() ([]byte, []int) {
	return fileDescriptor_7f4f7a260fb73706, []int{0}
}
func (m *CheckpointsModel) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CheckpointsModel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CheckpointsModel.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CheckpointsModel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckpointsModel.Merge(m, src)
}
func (m *CheckpointsModel) XXX_Size() int {
	return m.Size()
}
func (m *CheckpointsModel) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckpointsModel.DiscardUnknown(m)
}

var xxx_messageInfo_CheckpointsModel proto.InternalMessageInfo

type TaskCheckpointModel struct {
	TaskId       int64  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	SourceDir    string `protobuf:"bytes,2,opt,name=source_dir,json=sourceDir,proto3" json:"source_dir,omitempty"`
	Backend      string `protobuf:"bytes,3,opt,name=backend,proto3" json:"backend,omitempty"`
	ImporterAddr string `protobuf:"bytes,4,opt,name=importer_addr,json=importerAddr,proto3" json:"importer_addr,omitempty"`
	TidbHost     string `protobuf:"bytes,5,opt,name=tidb_host,json=tidbHost,proto3" json:"tidb_host,omitempty"`
	TidbPort     int32  `protobuf:"varint,6,opt,name=tidb_port,json=tidbPort,proto3" json:"tidb_port,omitempty"`
	PdAddr       string `protobuf:"bytes,7,opt,name=pd_addr,json=pdAddr,proto3" json:"pd_addr,omitempty"`
	SortedKvDir  string `protobuf:"bytes,8,opt,name=sorted_kv_dir,json=sortedKvDir,proto3" json:"sorted_kv_dir,omitempty"`
	LightningVer string `protobuf:"bytes,9,opt,name=lightning_ver,json=lightningVer,proto3" json:"lightning_ver,omitempty"`
}

func (m *TaskCheckpointModel) Reset()         { *m = TaskCheckpointModel{} }
func (m *TaskCheckpointModel) String() string { return proto.CompactTextString(m) }
func (*TaskCheckpointModel) ProtoMessage()    {}
func (*TaskCheckpointModel) Descriptor() ([]byte, []int) {
	return fileDescriptor_7f4f7a260fb73706, []int{1}
}
func (m *TaskCheckpointModel) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TaskCheckpointModel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TaskCheckpointModel.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TaskCheckpointModel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskCheckpointModel.Merge(m, src)
}
func (m *TaskCheckpointModel) XXX_Size() int {
	return m.Size()
}
func (m *TaskCheckpointModel) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskCheckpointModel.DiscardUnknown(m)
}

var xxx_messageInfo_TaskCheckpointModel proto.InternalMessageInfo

type TableCheckpointModel struct {
	Hash       []byte                           `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	Status     uint32                           `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	Engines    map[int32]*EngineCheckpointModel `protobuf:"bytes,8,rep,name=engines,proto3" json:"engines,omitempty" protobuf_key:"zigzag32,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TableID    int64                            `protobuf:"varint,9,opt,name=tableID,proto3" json:"tableID,omitempty"`
	KvBytes    uint64                           `protobuf:"varint,10,opt,name=kv_bytes,json=kvBytes,proto3" json:"kv_bytes,omitempty"`
	KvKvs      uint64                           `protobuf:"varint,11,opt,name=kv_kvs,json=kvKvs,proto3" json:"kv_kvs,omitempty"`
	KvChecksum uint64                           `protobuf:"fixed64,12,opt,name=kv_checksum,json=kvChecksum,proto3" json:"kv_checksum,omitempty"`
	TableInfo  []byte                           `protobuf:"bytes,13,opt,name=table_info,json=tableInfo,proto3" json:"table_info,omitempty"`
	// used to record the max auto random ID without the sharding bits that has been used.
	AutoRandBase int64 `protobuf:"varint,14,opt,name=autoRandBase,proto3" json:"autoRandBase,omitempty"`
	// used to record the max auto increment ID that has been used.
	AutoIncrBase int64 `protobuf:"varint,15,opt,name=autoIncrBase,proto3" json:"autoIncrBase,omitempty"`
	// used to record the max auto row ID that has been used.
	AutoRowIDBase int64 `protobuf:"varint,16,opt,name=autoRowIDBase,proto3" json:"autoRowIDBase,omitempty"`
}

func (m *TableCheckpointModel) Reset()         { *m = TableCheckpointModel{} }
func (m *TableCheckpointModel) String() string { return proto.CompactTextString(m) }
func (*TableCheckpointModel) ProtoMessage()    {}
func (*TableCheckpointModel) Descriptor() ([]byte, []int) {
	return fileDescriptor_7f4f7a260fb73706, []int{2}
}
func (m *TableCheckpointModel) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TableCheckpointModel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TableCheckpointModel.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TableCheckpointModel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TableCheckpointModel.Merge(m, src)
}
func (m *TableCheckpointModel) XXX_Size() int {
	return m.Size()
}
func (m *TableCheckpointModel) XXX_DiscardUnknown() {
	xxx_messageInfo_TableCheckpointModel.DiscardUnknown(m)
}

var xxx_messageInfo_TableCheckpointModel proto.InternalMessageInfo

type EngineCheckpointModel struct {
	Status uint32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	// key is "$path:$offset"
	Chunks map[string]*ChunkCheckpointModel `protobuf:"bytes,2,rep,name=chunks,proto3" json:"chunks,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (m *EngineCheckpointModel) Reset()         { *m = EngineCheckpointModel{} }
func (m *EngineCheckpointModel) String() string { return proto.CompactTextString(m) }
func (*EngineCheckpointModel) ProtoMessage()    {}
func (*EngineCheckpointModel) Descriptor() ([]byte, []int) {
	return fileDescriptor_7f4f7a260fb73706, []int{3}
}
func (m *EngineCheckpointModel) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EngineCheckpointModel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EngineCheckpointModel.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EngineCheckpointModel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EngineCheckpointModel.Merge(m, src)
}
func (m *EngineCheckpointModel) XXX_Size() int {
	return m.Size()
}
func (m *EngineCheckpointModel) XXX_DiscardUnknown() {
	xxx_messageInfo_EngineCheckpointModel.DiscardUnknown(m)
}

var xxx_messageInfo_EngineCheckpointModel proto.InternalMessageInfo

type ChunkCheckpointModel struct {
	Path              string  `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Offset            int64   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	ColumnPermutation []int32 `protobuf:"varint,12,rep,packed,name=column_permutation,json=columnPermutation,proto3" json:"column_permutation,omitempty"`
	EndOffset         int64   `protobuf:"varint,5,opt,name=end_offset,json=endOffset,proto3" json:"end_offset,omitempty"`
	Pos               int64   `protobuf:"varint,6,opt,name=pos,proto3" json:"pos,omitempty"`
	PrevRowidMax      int64   `protobuf:"varint,7,opt,name=prev_rowid_max,json=prevRowidMax,proto3" json:"prev_rowid_max,omitempty"`
	RowidMax          int64   `protobuf:"varint,8,opt,name=rowid_max,json=rowidMax,proto3" json:"rowid_max,omitempty"`
	KvcBytes          uint64  `protobuf:"varint,9,opt,name=kvc_bytes,json=kvcBytes,proto3" json:"kvc_bytes,omitempty"`
	KvcKvs            uint64  `protobuf:"varint,10,opt,name=kvc_kvs,json=kvcKvs,proto3" json:"kvc_kvs,omitempty"`
	KvcChecksum       uint64  `protobuf:"fixed64,11,opt,name=kvc_checksum,json=kvcChecksum,proto3" json:"kvc_checksum,omitempty"`
	Timestamp         int64   `protobuf:"fixed64,13,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Type              int32   `protobuf:"varint,14,opt,name=type,proto3" json:"type,omitempty"`
	Compression       int32   `protobuf:"varint,15,opt,name=compression,proto3" json:"compression,omitempty"`
	SortKey           string  `protobuf:"bytes,16,opt,name=sort_key,json=sortKey,proto3" json:"sort_key,omitempty"`
	FileSize          int64   `protobuf:"varint,17,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	RealPos           int64   `protobuf:"varint,18,opt,name=real_pos,json=realPos,proto3" json:"real_pos,omitempty"`
}

func (m *ChunkCheckpointModel) Reset()         { *m = ChunkCheckpointModel{} }
func (m *ChunkCheckpointModel) String() string { return proto.CompactTextString(m) }
func (*ChunkCheckpointModel) ProtoMessage()    {}
func (*ChunkCheckpointModel) Descriptor() ([]byte, []int) {
	return fileDescriptor_7f4f7a260fb73706, []int{4}
}
func (m *ChunkCheckpointModel) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChunkCheckpointModel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChunkCheckpointModel.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChunkCheckpointModel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChunkCheckpointModel.Merge(m, src)
}
func (m *ChunkCheckpointModel) XXX_Size() int {
	return m.Size()
}
func (m *ChunkCheckpointModel) XXX_DiscardUnknown() {
	xxx_messageInfo_ChunkCheckpointModel.DiscardUnknown(m)
}

var xxx_messageInfo_ChunkCheckpointModel proto.InternalMessageInfo

func init() {
	proto.RegisterType((*CheckpointsModel)(nil), "CheckpointsModel")
	proto.RegisterMapType((map[string]*TableCheckpointModel)(nil), "CheckpointsModel.CheckpointsEntry")
	proto.RegisterType((*TaskCheckpointModel)(nil), "TaskCheckpointModel")
	proto.RegisterType((*TableCheckpointModel)(nil), "TableCheckpointModel")
	proto.RegisterMapType((map[int32]*EngineCheckpointModel)(nil), "TableCheckpointModel.EnginesEntry")
	proto.RegisterType((*EngineCheckpointModel)(nil), "EngineCheckpointModel")
	proto.RegisterMapType((map[string]*ChunkCheckpointModel)(nil), "EngineCheckpointModel.ChunksEntry")
	proto.RegisterType((*ChunkCheckpointModel)(nil), "ChunkCheckpointModel")
}

func init() {
	proto.RegisterFile("br/pkg/lightning/checkpoints/checkpointspb/file_checkpoints.proto", fileDescriptor_7f4f7a260fb73706)
}

var fileDescriptor_7f4f7a260fb73706 = []byte{
	// 941 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x55, 0xcf, 0x6e, 0xdb, 0xc6,
	0x13, 0x36, 0x4d, 0xfd, 0x1d, 0x4a, 0xb6, 0xbc, 0x3f, 0x3b, 0xe1, 0xcf, 0x6d, 0x55, 0x55, 0xc9,
	0x41, 0x40, 0x5a, 0x09, 0x75, 0x7a, 0x28, 0x82, 0x16, 0x68, 0x6c, 0x07, 0xa8, 0x6b, 0x04, 0x35,
	0xd8, 0xb4, 0x87, 0x5e, 0x08, 0x8a, 0x5c, 0x49, 0xc4, 0x4a, 0x5c, 0x62, 0x77, 0xc9, 0x44, 0x79,
	0x8a, 0x3e, 0x46, 0x5f, 0xa2, 0xf7, 0x1c, 0x73, 0xe8, 0xa1, 0xc7, 0xd6, 0x46, 0xaf, 0x7d, 0x86,
	0x62, 0x67, 0x29, 0x89, 0x0a, 0xd4, 0xa2, 0xb7, 0x9d, 0x6f, 0xbe, 0xf9, 0x38, 0x3b, 0xfb, 0xed,
	0x12, 0x9e, 0x8e, 0xc5, 0x28, 0x65, 0xd3, 0xd1, 0x3c, 0x9e, 0xce, 0x54, 0x12, 0x27, 0xd3, 0x51,
	0x38, 0xa3, 0x21, 0x4b, 0x79, 0x9c, 0x28, 0x59, 0x5e, 0xa7, 0xe3, 0xd1, 0x24, 0x9e, 0x53, 0xbf,
	0x04, 0x0d, 0x53, 0xc1, 0x15, 0x3f, 0xfd, 0x6c, 0x1a, 0xab, 0x59, 0x36, 0x1e, 0x86, 0x7c, 0x31,
	0x9a, 0xf2, 0x29, 0x1f, 0x21, 0x3c, 0xce, 0x26, 0x5f, 0xe5, 0x9f, 0x0e, 0x1f, 0x0f, 0xcf, 0x10,
	0x44, 0x0c, 0x57, 0xa6, 0xaa, 0xff, 0x97, 0x05, 0x9d, 0x8b, 0x8d, 0xd6, 0x73, 0x1e, 0xd1, 0x39,
	0xb9, 0x04, 0xa7, 0xa4, 0xef, 0x5a, 0x3d, 0x7b, 0xe0, 0x9c, 0xf5, 0x87, 0xef, 0xf2, 0xca, 0xc0,
	0xb3, 0x44, 0x89, 0xa5, 0x57, 0x2e, 0x23, 0x5f, 0xc2, 0xa1, 0x0a, 0x24, 0x2b, 0xb5, 0xea, 0xee,
	0xf7, 0xac, 0x81, 0x73, 0x76, 0x3c, 0x7c, 0x11, 0x48, 0xb6, 0x29, 0x46, 0x31, 0xef, 0x40, 0x6d,
	0x81, 0xa7, 0xdf, 0x6f, 0x35, 0x86, 0xfa, 0xa4, 0x03, 0x36, 0xa3, 0x4b, 0xd7, 0xea, 0x59, 0x83,
	0xa6, 0xa7, 0x97, 0xe4, 0x11, 0x54, 0xf3, 0x60, 0x9e, 0xd1, 0x42, 0xfa, 0x64, 0xf8, 0x22, 0x18,
	0xcf, 0xe9, 0xbb, 0xda, 0x86, 0xf3, 0x64, 0xff, 0x73, 0xab, 0xff, 0xf3, 0x3e, 0xfc, 0x6f, 0xc7,
	0xe7, 0xc9, 0x7d, 0xa8, 0x63, 0xb7, 0x71, 0x84, 0xf2, 0xb6, 0x57, 0xd3, 0xe1, 0x55, 0x44, 0x3e,
	0x00, 0x90, 0x3c, 0x13, 0x21, 0xf5, 0xa3, 0x58, 0xe0, 0x67, 0x9a, 0x5e, 0xd3, 0x20, 0x97, 0xb1,
	0x20, 0x2e, 0xd4, 0xc7, 0x41, 0xc8, 0x68, 0x12, 0xb9, 0x36, 0xe6, 0x56, 0x21, 0x79, 0x00, 0xed,
	0x78, 0x91, 0x72, 0xa1, 0xa8, 0xf0, 0x83, 0x28, 0x12, 0x6e, 0x05, 0xf3, 0xad, 0x15, 0xf8, 0x34,
	0x8a, 0x04, 0x79, 0x0f, 0x9a, 0x2a, 0x8e, 0xc6, 0xfe, 0x8c, 0x4b, 0xe5, 0x56, 0x91, 0xd0, 0xd0,
	0xc0, 0xd7, 0x5c, 0xaa, 0x75, 0x52, 0xf3, 0xdd, 0x5a, 0xcf, 0x1a, 0x54, 0x4d, 0xf2, 0x86, 0x0b,
	0xa5, 0x1b, 0x4e, 0x23, 0x23, 0x5c, 0xc7, 0xba, 0x5a, 0x1a, 0xa1, 0x64, 0x1f, 0xda, 0x52, 0x7f,
	0x20, 0xf2, 0x59, 0x8e, 0x3d, 0x37, 0x30, 0xed, 0x18, 0xf0, 0x3a, 0xd7, 0x5d, 0x3f, 0x80, 0xf6,
	0xda, 0x6a, 0x7e, 0x4e, 0x85, 0xdb, 0x34, 0xbd, 0xad, 0xc1, 0x1f, 0xa8, 0xe8, 0xff, 0x6a, 0xc3,
	0xf1, 0xae, 0x71, 0x12, 0x02, 0x95, 0x59, 0x20, 0x67, 0x38, 0xa8, 0x96, 0x87, 0x6b, 0x72, 0x0f,
	0x6a, 0x52, 0x05, 0x2a, 0x93, 0x38, 0x86, 0xb6, 0x57, 0x44, 0xe4, 0x0b, 0xa8, 0xd3, 0x64, 0x1a,
	0x27, 0x54, 0xba, 0x8d, 0xc2, 0x47, 0xbb, 0x34, 0x87, 0xcf, 0x0c, 0xc9, 0xf8, 0x68, 0x55, 0xa2,
	0xa7, 0xab, 0x34, 0xfb, 0xea, 0x12, 0x3b, 0xb4, 0xbd, 0x55, 0x48, 0xfe, 0x0f, 0x0d, 0x96, 0xfb,
	0xe3, 0xa5, 0xa2, 0xd2, 0x85, 0x9e, 0x35, 0xa8, 0x78, 0x75, 0x96, 0x9f, 0xeb, 0x90, 0x9c, 0x40,
	0x8d, 0xe5, 0x3e, 0xcb, 0xa5, 0xeb, 0x60, 0xa2, 0xca, 0xf2, 0xeb, 0x5c, 0x92, 0x0f, 0xc1, 0x61,
	0xb9, 0x71, 0xa3, 0xcc, 0x16, 0x6e, 0xab, 0x67, 0x0d, 0x6a, 0x1e, 0xb0, 0xfc, 0xa2, 0x40, 0xf4,
	0x49, 0xa3, 0xba, 0x1f, 0x27, 0x13, 0xee, 0xb6, 0x71, 0x73, 0x4d, 0xf3, 0xbd, 0x64, 0xc2, 0x49,
	0x1f, 0x5a, 0x41, 0xa6, 0xb8, 0x17, 0x24, 0xd1, 0x79, 0x20, 0xa9, 0x7b, 0x80, 0x0d, 0x6d, 0x61,
	0x2b, 0xce, 0x55, 0x12, 0x0a, 0xe4, 0x1c, 0x6e, 0x38, 0x2b, 0x8c, 0x3c, 0x84, 0x36, 0xd6, 0xf0,
	0x97, 0x57, 0x97, 0x48, 0xea, 0x20, 0x69, 0x1b, 0x3c, 0xf5, 0xa0, 0x55, 0x1e, 0x49, 0xd9, 0xfa,
	0x47, 0xc6, 0xfa, 0x1f, 0x6f, 0x5b, 0xff, 0x5e, 0x31, 0xc2, 0x7f, 0xf6, 0xfe, 0x37, 0x95, 0x46,
	0xa5, 0x53, 0xed, 0xff, 0x62, 0xc1, 0xc9, 0x4e, 0x6a, 0xe9, 0x0c, 0xad, 0xad, 0x33, 0x7c, 0x02,
	0xb5, 0x70, 0x96, 0x25, 0x4c, 0xba, 0xfb, 0xc5, 0x11, 0xee, 0xac, 0x1f, 0x5e, 0x20, 0xc9, 0x1c,
	0x61, 0x51, 0x71, 0x7a, 0x03, 0x4e, 0x09, 0xfe, 0x2f, 0x37, 0x18, 0xe9, 0xff, 0x72, 0x83, 0xff,
	0xb4, 0xe1, 0x78, 0x17, 0x47, 0xdb, 0x32, 0x0d, 0xd4, 0xac, 0x10, 0xc7, 0xb5, 0xde, 0x12, 0x9f,
	0x4c, 0x24, 0x35, 0x6f, 0x8f, 0xed, 0x15, 0x11, 0xf9, 0x04, 0x48, 0xc8, 0xe7, 0xd9, 0x22, 0xf1,
	0x53, 0x2a, 0x16, 0x99, 0x0a, 0x54, 0xcc, 0x13, 0xb7, 0xd5, 0xb3, 0x07, 0x55, 0xef, 0xc8, 0x64,
	0x6e, 0x36, 0x09, 0x6d, 0x0d, 0x9a, 0x44, 0x7e, 0x21, 0x55, 0x45, 0xa9, 0x26, 0x4d, 0xa2, 0x6f,
	0x8d, 0x5a, 0x07, 0xec, 0x94, 0x4b, 0xbc, 0xa2, 0xb6, 0xa7, 0x97, 0xe4, 0x21, 0x1c, 0xa4, 0x82,
	0xe6, 0xbe, 0xe0, 0x2f, 0xe3, 0xc8, 0x5f, 0x04, 0xaf, 0xf0, 0x92, 0xda, 0x5e, 0x4b, 0xa3, 0x9e,
	0x06, 0x9f, 0x07, 0xaf, 0xf4, 0x05, 0xdf, 0x10, 0x1a, 0x48, 0x68, 0x88, 0x52, 0x92, 0xe5, 0x61,
	0x61, 0xf1, 0x26, 0x3a, 0xb9, 0xc1, 0xf2, 0xd0, 0x78, 0xfc, 0x3e, 0xd4, 0x75, 0x52, 0x9b, 0xdc,
	0xb8, 0xbf, 0xc6, 0xf2, 0x50, 0xbb, 0xfc, 0x23, 0x68, 0xe9, 0xc4, 0xda, 0xe6, 0x0e, 0xda, 0xdc,
	0x61, 0x79, 0xb8, 0xf6, 0xf9, 0xfb, 0xfa, 0x59, 0x59, 0x50, 0xa9, 0x82, 0x45, 0x8a, 0x36, 0xef,
	0x78, 0x1b, 0x40, 0x4f, 0x51, 0x2d, 0x53, 0x63, 0xef, 0xaa, 0x87, 0x6b, 0xd2, 0x03, 0x27, 0xe4,
	0x8b, 0x54, 0x50, 0x29, 0xf5, 0x98, 0x0e, 0x31, 0x55, 0x86, 0xf4, 0x75, 0xd4, 0xef, 0x8b, 0xaf,
	0x0f, 0xb7, 0x63, 0xde, 0x41, 0x1d, 0x5f, 0xd3, 0xa5, 0xde, 0x07, 0xfe, 0xb2, 0x64, 0xfc, 0x9a,
	0xba, 0x47, 0x66, 0x93, 0x1a, 0xf8, 0x2e, 0x7e, 0x4d, 0x75, 0x9d, 0xa0, 0xc1, 0xdc, 0xd7, 0xe3,
	0x23, 0xe6, 0x86, 0xeb, 0xf8, 0x86, 0xcb, 0xf3, 0x47, 0x6f, 0xfe, 0xe8, 0xee, 0xbd, 0xb9, 0xed,
	0x5a, 0x6f, 0x6f, 0xbb, 0xd6, 0xef, 0xb7, 0x5d, 0xeb, 0xa7, 0xbb, 0xee, 0xde, 0xdb, 0xbb, 0xee,
	0xde, 0x6f, 0x77, 0xdd, 0xbd, 0x1f, 0xdb, 0x5b, 0x3f, 0xc5, 0x71, 0x0d, 0x7f, 0x67, 0x8f, 0xff,
	0x0e, 0x00, 0x00, 0xff, 0xff, 0x84, 0x1d, 0xd9, 0xff, 0x49, 0x07, 0x00, 0x00,
}

func (m *CheckpointsModel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckpointsModel) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CheckpointsModel) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TaskCheckpoint != nil {
		{
			size, err := m.TaskCheckpoint.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintFileCheckpoints(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.Checkpoints) > 0 {
		for k := range m.Checkpoints {
			v := m.Checkpoints[k]
			baseI := i
			if v != nil {
				{
					size, err := v.MarshalToSizedBuffer(dAtA[:i])
					if err != nil {
						return 0, err
					}
					i -= size
					i = encodeVarintFileCheckpoints(dAtA, i, uint64(size))
				}
				i--
				dAtA[i] = 0x12
			}
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintFileCheckpoints(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *TaskCheckpointModel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TaskCheckpointModel) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TaskCheckpointModel) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.LightningVer) > 0 {
		i -= len(m.LightningVer)
		copy(dAtA[i:], m.LightningVer)
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(m.LightningVer)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.SortedKvDir) > 0 {
		i -= len(m.SortedKvDir)
		copy(dAtA[i:], m.SortedKvDir)
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(m.SortedKvDir)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.PdAddr) > 0 {
		i -= len(m.PdAddr)
		copy(dAtA[i:], m.PdAddr)
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(m.PdAddr)))
		i--
		dAtA[i] = 0x3a
	}
	if m.TidbPort != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.TidbPort))
		i--
		dAtA[i] = 0x30
	}
	if len(m.TidbHost) > 0 {
		i -= len(m.TidbHost)
		copy(dAtA[i:], m.TidbHost)
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(m.TidbHost)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.ImporterAddr) > 0 {
		i -= len(m.ImporterAddr)
		copy(dAtA[i:], m.ImporterAddr)
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(m.ImporterAddr)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Backend) > 0 {
		i -= len(m.Backend)
		copy(dAtA[i:], m.Backend)
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(m.Backend)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.SourceDir) > 0 {
		i -= len(m.SourceDir)
		copy(dAtA[i:], m.SourceDir)
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(m.SourceDir)))
		i--
		dAtA[i] = 0x12
	}
	if m.TaskId != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.TaskId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *TableCheckpointModel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TableCheckpointModel) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TableCheckpointModel) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AutoRowIDBase != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.AutoRowIDBase))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x80
	}
	if m.AutoIncrBase != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.AutoIncrBase))
		i--
		dAtA[i] = 0x78
	}
	if m.AutoRandBase != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.AutoRandBase))
		i--
		dAtA[i] = 0x70
	}
	if len(m.TableInfo) > 0 {
		i -= len(m.TableInfo)
		copy(dAtA[i:], m.TableInfo)
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(m.TableInfo)))
		i--
		dAtA[i] = 0x6a
	}
	if m.KvChecksum != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.KvChecksum))
		i--
		dAtA[i] = 0x61
	}
	if m.KvKvs != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.KvKvs))
		i--
		dAtA[i] = 0x58
	}
	if m.KvBytes != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.KvBytes))
		i--
		dAtA[i] = 0x50
	}
	if m.TableID != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.TableID))
		i--
		dAtA[i] = 0x48
	}
	if len(m.Engines) > 0 {
		for k := range m.Engines {
			v := m.Engines[k]
			baseI := i
			if v != nil {
				{
					size, err := v.MarshalToSizedBuffer(dAtA[:i])
					if err != nil {
						return 0, err
					}
					i -= size
					i = encodeVarintFileCheckpoints(dAtA, i, uint64(size))
				}
				i--
				dAtA[i] = 0x12
			}
			i = encodeVarintFileCheckpoints(dAtA, i, uint64((uint32(k)<<1)^uint32((k>>31))))
			i--
			dAtA[i] = 0x8
			i = encodeVarintFileCheckpoints(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x42
		}
	}
	if m.Status != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.Status))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Hash) > 0 {
		i -= len(m.Hash)
		copy(dAtA[i:], m.Hash)
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(m.Hash)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *EngineCheckpointModel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EngineCheckpointModel) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EngineCheckpointModel) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Chunks) > 0 {
		for k := range m.Chunks {
			v := m.Chunks[k]
			baseI := i
			if v != nil {
				{
					size, err := v.MarshalToSizedBuffer(dAtA[:i])
					if err != nil {
						return 0, err
					}
					i -= size
					i = encodeVarintFileCheckpoints(dAtA, i, uint64(size))
				}
				i--
				dAtA[i] = 0x12
			}
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintFileCheckpoints(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Status != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.Status))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ChunkCheckpointModel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChunkCheckpointModel) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChunkCheckpointModel) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RealPos != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.RealPos))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x90
	}
	if m.FileSize != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.FileSize))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x88
	}
	if len(m.SortKey) > 0 {
		i -= len(m.SortKey)
		copy(dAtA[i:], m.SortKey)
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(m.SortKey)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if m.Compression != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.Compression))
		i--
		dAtA[i] = 0x78
	}
	if m.Type != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x70
	}
	if m.Timestamp != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Timestamp))
		i--
		dAtA[i] = 0x69
	}
	if len(m.ColumnPermutation) > 0 {
		dAtA6 := make([]byte, len(m.ColumnPermutation)*10)
		var j5 int
		for _, num1 := range m.ColumnPermutation {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA6[j5] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j5++
			}
			dAtA6[j5] = uint8(num)
			j5++
		}
		i -= j5
		copy(dAtA[i:], dAtA6[:j5])
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(j5))
		i--
		dAtA[i] = 0x62
	}
	if m.KvcChecksum != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.KvcChecksum))
		i--
		dAtA[i] = 0x59
	}
	if m.KvcKvs != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.KvcKvs))
		i--
		dAtA[i] = 0x50
	}
	if m.KvcBytes != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.KvcBytes))
		i--
		dAtA[i] = 0x48
	}
	if m.RowidMax != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.RowidMax))
		i--
		dAtA[i] = 0x40
	}
	if m.PrevRowidMax != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.PrevRowidMax))
		i--
		dAtA[i] = 0x38
	}
	if m.Pos != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.Pos))
		i--
		dAtA[i] = 0x30
	}
	if m.EndOffset != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.EndOffset))
		i--
		dAtA[i] = 0x28
	}
	if m.Offset != 0 {
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(m.Offset))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Path) > 0 {
		i -= len(m.Path)
		copy(dAtA[i:], m.Path)
		i = encodeVarintFileCheckpoints(dAtA, i, uint64(len(m.Path)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintFileCheckpoints(dAtA []byte, offset int, v uint64) int {
	offset -= sovFileCheckpoints(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *CheckpointsModel) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Checkpoints) > 0 {
		for k, v := range m.Checkpoints {
			_ = k
			_ = v
			l = 0
			if v != nil {
				l = v.Size()
				l += 1 + sovFileCheckpoints(uint64(l))
			}
			mapEntrySize := 1 + len(k) + sovFileCheckpoints(uint64(len(k))) + l
			n += mapEntrySize + 1 + sovFileCheckpoints(uint64(mapEntrySize))
		}
	}
	if m.TaskCheckpoint != nil {
		l = m.TaskCheckpoint.Size()
		n += 1 + l + sovFileCheckpoints(uint64(l))
	}
	return n
}

func (m *TaskCheckpointModel) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TaskId != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.TaskId))
	}
	l = len(m.SourceDir)
	if l > 0 {
		n += 1 + l + sovFileCheckpoints(uint64(l))
	}
	l = len(m.Backend)
	if l > 0 {
		n += 1 + l + sovFileCheckpoints(uint64(l))
	}
	l = len(m.ImporterAddr)
	if l > 0 {
		n += 1 + l + sovFileCheckpoints(uint64(l))
	}
	l = len(m.TidbHost)
	if l > 0 {
		n += 1 + l + sovFileCheckpoints(uint64(l))
	}
	if m.TidbPort != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.TidbPort))
	}
	l = len(m.PdAddr)
	if l > 0 {
		n += 1 + l + sovFileCheckpoints(uint64(l))
	}
	l = len(m.SortedKvDir)
	if l > 0 {
		n += 1 + l + sovFileCheckpoints(uint64(l))
	}
	l = len(m.LightningVer)
	if l > 0 {
		n += 1 + l + sovFileCheckpoints(uint64(l))
	}
	return n
}

func (m *TableCheckpointModel) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Hash)
	if l > 0 {
		n += 1 + l + sovFileCheckpoints(uint64(l))
	}
	if m.Status != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.Status))
	}
	if len(m.Engines) > 0 {
		for k, v := range m.Engines {
			_ = k
			_ = v
			l = 0
			if v != nil {
				l = v.Size()
				l += 1 + sovFileCheckpoints(uint64(l))
			}
			mapEntrySize := 1 + sozFileCheckpoints(uint64(k)) + l
			n += mapEntrySize + 1 + sovFileCheckpoints(uint64(mapEntrySize))
		}
	}
	if m.TableID != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.TableID))
	}
	if m.KvBytes != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.KvBytes))
	}
	if m.KvKvs != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.KvKvs))
	}
	if m.KvChecksum != 0 {
		n += 9
	}
	l = len(m.TableInfo)
	if l > 0 {
		n += 1 + l + sovFileCheckpoints(uint64(l))
	}
	if m.AutoRandBase != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.AutoRandBase))
	}
	if m.AutoIncrBase != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.AutoIncrBase))
	}
	if m.AutoRowIDBase != 0 {
		n += 2 + sovFileCheckpoints(uint64(m.AutoRowIDBase))
	}
	return n
}

func (m *EngineCheckpointModel) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Status != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.Status))
	}
	if len(m.Chunks) > 0 {
		for k, v := range m.Chunks {
			_ = k
			_ = v
			l = 0
			if v != nil {
				l = v.Size()
				l += 1 + sovFileCheckpoints(uint64(l))
			}
			mapEntrySize := 1 + len(k) + sovFileCheckpoints(uint64(len(k))) + l
			n += mapEntrySize + 1 + sovFileCheckpoints(uint64(mapEntrySize))
		}
	}
	return n
}

func (m *ChunkCheckpointModel) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Path)
	if l > 0 {
		n += 1 + l + sovFileCheckpoints(uint64(l))
	}
	if m.Offset != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.Offset))
	}
	if m.EndOffset != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.EndOffset))
	}
	if m.Pos != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.Pos))
	}
	if m.PrevRowidMax != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.PrevRowidMax))
	}
	if m.RowidMax != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.RowidMax))
	}
	if m.KvcBytes != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.KvcBytes))
	}
	if m.KvcKvs != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.KvcKvs))
	}
	if m.KvcChecksum != 0 {
		n += 9
	}
	if len(m.ColumnPermutation) > 0 {
		l = 0
		for _, e := range m.ColumnPermutation {
			l += sovFileCheckpoints(uint64(e))
		}
		n += 1 + sovFileCheckpoints(uint64(l)) + l
	}
	if m.Timestamp != 0 {
		n += 9
	}
	if m.Type != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.Type))
	}
	if m.Compression != 0 {
		n += 1 + sovFileCheckpoints(uint64(m.Compression))
	}
	l = len(m.SortKey)
	if l > 0 {
		n += 2 + l + sovFileCheckpoints(uint64(l))
	}
	if m.FileSize != 0 {
		n += 2 + sovFileCheckpoints(uint64(m.FileSize))
	}
	if m.RealPos != 0 {
		n += 2 + sovFileCheckpoints(uint64(m.RealPos))
	}
	return n
}

func sovFileCheckpoints(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozFileCheckpoints(x uint64) (n int) {
	return sovFileCheckpoints(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *CheckpointsModel) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFileCheckpoints
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CheckpointsModel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CheckpointsModel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Checkpoints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Checkpoints == nil {
				m.Checkpoints = make(map[string]*TableCheckpointModel)
			}
			var mapkey string
			var mapvalue *TableCheckpointModel
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFileCheckpoints
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFileCheckpoints
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var mapmsglen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFileCheckpoints
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapmsglen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if mapmsglen < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					postmsgIndex := iNdEx + mapmsglen
					if postmsgIndex < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					if postmsgIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = &TableCheckpointModel{}
					if err := mapvalue.Unmarshal(dAtA[iNdEx:postmsgIndex]); err != nil {
						return err
					}
					iNdEx = postmsgIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipFileCheckpoints(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Checkpoints[mapkey] = mapvalue
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskCheckpoint", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TaskCheckpoint == nil {
				m.TaskCheckpoint = &TaskCheckpointModel{}
			}
			if err := m.TaskCheckpoint.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFileCheckpoints(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TaskCheckpointModel) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFileCheckpoints
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TaskCheckpointModel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TaskCheckpointModel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			m.TaskId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TaskId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceDir", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceDir = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Backend", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Backend = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImporterAddr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImporterAddr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TidbHost", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TidbHost = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TidbPort", wireType)
			}
			m.TidbPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TidbPort |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PdAddr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PdAddr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SortedKvDir", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SortedKvDir = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LightningVer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LightningVer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFileCheckpoints(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TableCheckpointModel) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFileCheckpoints
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TableCheckpointModel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TableCheckpointModel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hash", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Hash = append(m.Hash[:0], dAtA[iNdEx:postIndex]...)
			if m.Hash == nil {
				m.Hash = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Engines", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Engines == nil {
				m.Engines = make(map[int32]*EngineCheckpointModel)
			}
			var mapkey int32
			var mapvalue *EngineCheckpointModel
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFileCheckpoints
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var mapkeytemp int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFileCheckpoints
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapkeytemp |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					mapkeytemp = int32((uint32(mapkeytemp) >> 1) ^ uint32(((mapkeytemp&1)<<31)>>31))
					mapkey = int32(mapkeytemp)
				} else if fieldNum == 2 {
					var mapmsglen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFileCheckpoints
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapmsglen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if mapmsglen < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					postmsgIndex := iNdEx + mapmsglen
					if postmsgIndex < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					if postmsgIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = &EngineCheckpointModel{}
					if err := mapvalue.Unmarshal(dAtA[iNdEx:postmsgIndex]); err != nil {
						return err
					}
					iNdEx = postmsgIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipFileCheckpoints(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Engines[mapkey] = mapvalue
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableID", wireType)
			}
			m.TableID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TableID |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KvBytes", wireType)
			}
			m.KvBytes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KvBytes |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KvKvs", wireType)
			}
			m.KvKvs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KvKvs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field KvChecksum", wireType)
			}
			m.KvChecksum = 0
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			m.KvChecksum = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableInfo = append(m.TableInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.TableInfo == nil {
				m.TableInfo = []byte{}
			}
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AutoRandBase", wireType)
			}
			m.AutoRandBase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AutoRandBase |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AutoIncrBase", wireType)
			}
			m.AutoIncrBase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AutoIncrBase |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AutoRowIDBase", wireType)
			}
			m.AutoRowIDBase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AutoRowIDBase |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFileCheckpoints(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EngineCheckpointModel) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFileCheckpoints
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EngineCheckpointModel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EngineCheckpointModel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Chunks == nil {
				m.Chunks = make(map[string]*ChunkCheckpointModel)
			}
			var mapkey string
			var mapvalue *ChunkCheckpointModel
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFileCheckpoints
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFileCheckpoints
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var mapmsglen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFileCheckpoints
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapmsglen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if mapmsglen < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					postmsgIndex := iNdEx + mapmsglen
					if postmsgIndex < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					if postmsgIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = &ChunkCheckpointModel{}
					if err := mapvalue.Unmarshal(dAtA[iNdEx:postmsgIndex]); err != nil {
						return err
					}
					iNdEx = postmsgIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipFileCheckpoints(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthFileCheckpoints
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Chunks[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFileCheckpoints(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChunkCheckpointModel) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFileCheckpoints
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChunkCheckpointModel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChunkCheckpointModel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Path", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Path = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndOffset", wireType)
			}
			m.EndOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndOffset |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pos", wireType)
			}
			m.Pos = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Pos |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PrevRowidMax", wireType)
			}
			m.PrevRowidMax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PrevRowidMax |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RowidMax", wireType)
			}
			m.RowidMax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RowidMax |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KvcBytes", wireType)
			}
			m.KvcBytes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KvcBytes |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KvcKvs", wireType)
			}
			m.KvcKvs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KvcKvs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field KvcChecksum", wireType)
			}
			m.KvcChecksum = 0
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			m.KvcChecksum = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
		case 12:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFileCheckpoints
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ColumnPermutation = append(m.ColumnPermutation, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFileCheckpoints
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFileCheckpoints
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthFileCheckpoints
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.ColumnPermutation) == 0 {
					m.ColumnPermutation = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFileCheckpoints
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ColumnPermutation = append(m.ColumnPermutation, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field ColumnPermutation", wireType)
			}
		case 13:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			m.Timestamp = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Compression", wireType)
			}
			m.Compression = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Compression |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SortKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SortKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileSize", wireType)
			}
			m.FileSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FileSize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 18:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RealPos", wireType)
			}
			m.RealPos = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RealPos |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFileCheckpoints(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFileCheckpoints
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipFileCheckpoints(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowFileCheckpoints
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFileCheckpoints
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthFileCheckpoints
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupFileCheckpoints
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthFileCheckpoints
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthFileCheckpoints        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowFileCheckpoints          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupFileCheckpoints = fmt.Errorf("proto: unexpected end of group")
)
