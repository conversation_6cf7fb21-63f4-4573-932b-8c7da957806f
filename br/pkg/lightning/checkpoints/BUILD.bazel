load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "checkpoints",
    srcs = [
        "checkpoints.go",
        "glue_checkpoint.go",
        "tidb.go",
    ],
    importpath = "github.com/pingcap/tidb/br/pkg/lightning/checkpoints",
    visibility = ["//visibility:public"],
    deps = [
        "//br/pkg/lightning/checkpoints/checkpointspb",
        "//br/pkg/lightning/common",
        "//br/pkg/lightning/config",
        "//br/pkg/lightning/log",
        "//br/pkg/lightning/mydump",
        "//br/pkg/lightning/verification",
        "//br/pkg/storage",
        "//br/pkg/version/build",
        "//pkg/parser/ast",
        "//pkg/parser/model",
        "//pkg/types",
        "//pkg/util/chunk",
        "//pkg/util/sqlexec",
        "@com_github_joho_sqltocsv//:sqltocsv",
        "@com_github_pingcap_errors//:errors",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "checkpoints_test",
    timeout = "short",
    srcs = [
        "checkpoints_file_test.go",
        "checkpoints_sql_test.go",
        "checkpoints_test.go",
        "main_test.go",
    ],
    embed = [":checkpoints"],
    flaky = True,
    race = "on",
    shard_count = 24,
    deps = [
        "//br/pkg/lightning/checkpoints/checkpointspb",
        "//br/pkg/lightning/config",
        "//br/pkg/lightning/mydump",
        "//br/pkg/lightning/verification",
        "//br/pkg/version/build",
        "//pkg/parser/model",
        "//pkg/testkit/testsetup",
        "@com_github_data_dog_go_sqlmock//:go-sqlmock",
        "@com_github_pingcap_errors//:errors",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_goleak//:goleak",
    ],
)
