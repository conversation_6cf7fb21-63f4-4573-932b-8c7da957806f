load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "config",
    srcs = [
        "bytesize.go",
        "config.go",
        "configlist.go",
        "const.go",
        "global.go",
    ],
    importpath = "github.com/pingcap/tidb/br/pkg/lightning/config",
    visibility = ["//visibility:public"],
    deps = [
        "//br/pkg/lightning/common",
        "//br/pkg/lightning/log",
        "//br/pkg/pdutil",
        "//br/pkg/version/build",
        "//pkg/config",
        "//pkg/parser/ast",
        "//pkg/parser/mysql",
        "//pkg/util",
        "//pkg/util/intest",
        "//pkg/util/mathutil",
        "//pkg/util/table-filter",
        "//pkg/util/table-router",
        "@com_github_burntsushi_toml//:toml",
        "@com_github_car<PERSON><PERSON><PERSON><PERSON>_flagext//:flagext",
        "@com_github_docker_go_units//:go-units",
        "@com_github_go_sql_driver_mysql//:mysql",
        "@com_github_pingcap_errors//:errors",
        "@com_github_tikv_client_go_v2//oracle",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//keepalive",
        "@org_uber_go_atomic//:atomic",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "config_test",
    timeout = "short",
    srcs = [
        "bytesize_test.go",
        "config_test.go",
        "configlist_test.go",
    ],
    embed = [":config"],
    flaky = True,
    shard_count = 49,
    deps = [
        "//br/pkg/lightning/common",
        "@com_github_burntsushi_toml//:toml",
        "@com_github_stretchr_testify//require",
    ],
)
