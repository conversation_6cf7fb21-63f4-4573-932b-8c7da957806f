load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "streamhelper",
    srcs = [
        "advancer.go",
        "advancer_cliext.go",
        "advancer_daemon.go",
        "advancer_env.go",
        "client.go",
        "collector.go",
        "flush_subscriber.go",
        "models.go",
        "prefix_scanner.go",
        "regioniter.go",
    ],
    importpath = "github.com/pingcap/tidb/br/pkg/streamhelper",
    visibility = ["//visibility:public"],
    deps = [
        "//br/pkg/errors",
        "//br/pkg/logutil",
        "//br/pkg/redact",
        "//br/pkg/streamhelper/config",
        "//br/pkg/streamhelper/spans",
        "//br/pkg/utils",
        "//pkg/config",
        "//pkg/kv",
        "//pkg/metrics",
        "//pkg/owner",
        "//pkg/util/codec",
        "//pkg/util/engine",
        "//pkg/util/mathutil",
        "//pkg/util/redact",
        "@com_github_gogo_protobuf//proto",
        "@com_github_golang_protobuf//proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_pingcap_kvproto//pkg/brpb",
        "@com_github_pingcap_kvproto//pkg/logbackuppb",
        "@com_github_pingcap_kvproto//pkg/metapb",
        "@com_github_pingcap_log//:log",
        "@com_github_tikv_client_go_v2//kv",
        "@com_github_tikv_client_go_v2//oracle",
        "@com_github_tikv_client_go_v2//tikv",
        "@com_github_tikv_client_go_v2//txnkv/rangetask",
        "@com_github_tikv_client_go_v2//txnkv/txnlock",
        "@com_github_tikv_pd_client//:client",
        "@com_github_tikv_pd_client//opt",
        "@io_etcd_go_etcd_client_v3//:client",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//keepalive",
        "@org_golang_google_grpc//status",
        "@org_golang_x_sync//errgroup",
        "@org_uber_go_multierr//:multierr",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "streamhelper_test",
    timeout = "short",
    srcs = [
        "advancer_test.go",
        "basic_lib_for_test.go",
        "integration_test.go",
        "regioniter_test.go",
        "subscription_test.go",
    ],
    flaky = True,
    race = "on",
    shard_count = 34,
    deps = [
        ":streamhelper",
        "//br/pkg/errors",
        "//br/pkg/logutil",
        "//br/pkg/redact",
        "//br/pkg/storage",
        "//br/pkg/streamhelper/config",
        "//br/pkg/streamhelper/spans",
        "//br/pkg/utils",
        "//pkg/kv",
        "//pkg/tablecodec",
        "//pkg/util/codec",
        "//pkg/util/redact",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_pingcap_kvproto//pkg/brpb",
        "@com_github_pingcap_kvproto//pkg/errorpb",
        "@com_github_pingcap_kvproto//pkg/kvrpcpb",
        "@com_github_pingcap_kvproto//pkg/logbackuppb",
        "@com_github_pingcap_kvproto//pkg/metapb",
        "@com_github_pingcap_log//:log",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_tikv_client_go_v2//kv",
        "@com_github_tikv_client_go_v2//oracle",
        "@com_github_tikv_client_go_v2//tikv",
        "@com_github_tikv_client_go_v2//tikvrpc",
        "@com_github_tikv_client_go_v2//txnkv/txnlock",
        "@com_github_tikv_pd_client//:client",
        "@com_github_tikv_pd_client//clients/router",
        "@com_github_tikv_pd_client//opt",
        "@io_etcd_go_etcd_client_v3//:client",
        "@io_etcd_go_etcd_server_v3//embed",
        "@io_etcd_go_etcd_server_v3//mvcc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
        "@org_uber_go_atomic//:atomic",
        "@org_uber_go_zap//:zap",
        "@org_uber_go_zap//zapcore",
    ],
)
