load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "storage",
    srcs = [
        "azblob.go",
        "compress.go",
        "flags.go",
        "gcs.go",
        "hdfs.go",
        "helper.go",
        "ks3.go",
        "local.go",
        "local_unix.go",
        "local_windows.go",
        "locking.go",
        "memstore.go",
        "mook.go",
        "noop.go",
        "oss_oidc_provider.go",
        "parse.go",
        "s3.go",
        "storage.go",
        "writer.go",
    ],
    importpath = "github.com/pingcap/tidb/br/pkg/storage",
    visibility = ["//visibility:public"],
    deps = [
        "//br/pkg/errors",
        "//br/pkg/lightning/log",
        "//br/pkg/logutil",
        "//pkg/util/intest",
        "@com_github_aliyun_alibabacloud_oss_go_sdk_v2//oss",
        "@com_github_aliyun_alibabacloud_oss_go_sdk_v2//oss/credentials",
        "@com_github_aws_aws_sdk_go//aws",
        "@com_github_aws_aws_sdk_go//aws/awserr",
        "@com_github_aws_aws_sdk_go//aws/client",
        "@com_github_aws_aws_sdk_go//aws/credentials",
        "@com_github_aws_aws_sdk_go//aws/credentials/stscreds",
        "@com_github_aws_aws_sdk_go//aws/request",
        "@com_github_aws_aws_sdk_go//aws/session",
        "@com_github_aws_aws_sdk_go//service/s3",
        "@com_github_aws_aws_sdk_go//service/s3/s3iface",
        "@com_github_aws_aws_sdk_go//service/s3/s3manager",
        "@com_github_azure_azure_sdk_for_go_sdk_azcore//:azcore",
        "@com_github_azure_azure_sdk_for_go_sdk_azcore//policy",
        "@com_github_azure_azure_sdk_for_go_sdk_azidentity//:azidentity",
        "@com_github_azure_azure_sdk_for_go_sdk_storage_azblob//:azblob",
        "@com_github_azure_azure_sdk_for_go_sdk_storage_azblob//blob",
        "@com_github_azure_azure_sdk_for_go_sdk_storage_azblob//bloberror",
        "@com_github_azure_azure_sdk_for_go_sdk_storage_azblob//blockblob",
        "@com_github_azure_azure_sdk_for_go_sdk_storage_azblob//container",
        "@com_github_google_uuid//:uuid",
        "@com_github_klauspost_compress//gzip",
        "@com_github_klauspost_compress//snappy",
        "@com_github_klauspost_compress//zstd",
        "@com_github_ks3sdklib_aws_sdk_go//aws",
        "@com_github_ks3sdklib_aws_sdk_go//aws/awserr",
        "@com_github_ks3sdklib_aws_sdk_go//aws/credentials",
        "@com_github_ks3sdklib_aws_sdk_go//service/s3",
        "@com_github_ks3sdklib_aws_sdk_go//service/s3/s3manager",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_pingcap_kvproto//pkg/brpb",
        "@com_github_pingcap_log//:log",
        "@com_github_spf13_pflag//:pflag",
        "@com_google_cloud_go_storage//:storage",
        "@org_golang_google_api//googleapi",
        "@org_golang_google_api//iterator",
        "@org_golang_google_api//option",
        "@org_golang_x_oauth2//google",
        "@org_uber_go_atomic//:atomic",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "storage_test",
    timeout = "short",
    srcs = [
        "azblob_test.go",
        "compress_test.go",
        "gcs_test.go",
        "local_test.go",
        "locking_test.go",
        "memstore_test.go",
        "parse_test.go",
        "s3_test.go",
        "storage_test.go",
        "writer_test.go",
    ],
    embed = [":storage"],
    flaky = True,
    shard_count = 50,
    deps = [
        "//br/pkg/mock",
        "@com_github_aws_aws_sdk_go//aws",
        "@com_github_aws_aws_sdk_go//aws/awserr",
        "@com_github_aws_aws_sdk_go//aws/request",
        "@com_github_aws_aws_sdk_go//service/s3",
        "@com_github_azure_azure_sdk_for_go_sdk_storage_azblob//:azblob",
        "@com_github_azure_azure_sdk_for_go_sdk_storage_azblob//bloberror",
        "@com_github_fsouza_fake_gcs_server//fakestorage",
        "@com_github_klauspost_compress//zstd",
        "@com_github_pingcap_errors//:errors",
        "@com_github_pingcap_failpoint//:failpoint",
        "@com_github_pingcap_kvproto//pkg/brpb",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_mock//gomock",
    ],
)
