load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "prealloc_table_id",
    srcs = ["alloc.go"],
    importpath = "github.com/pingcap/tidb/br/pkg/restore/prealloc_table_id",
    visibility = ["//visibility:public"],
    deps = [
        "//br/pkg/metautil",
        "//pkg/parser/model",
    ],
)

go_test(
    name = "prealloc_table_id_test",
    timeout = "short",
    srcs = ["alloc_test.go"],
    flaky = True,
    race = "on",
    deps = [
        ":prealloc_table_id",
        "//br/pkg/metautil",
        "//br/pkg/mock",
        "//pkg/kv",
        "//pkg/meta",
        "//pkg/parser/model",
        "//pkg/testkit",
        "@com_github_stretchr_testify//require",
    ],
)
