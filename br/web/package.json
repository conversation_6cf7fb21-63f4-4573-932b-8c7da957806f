{"name": "tidb-lightning-web", "version": "4.0.6", "description": "Web interface for TiDB Lightning", "author": "PingCAP, Inc.", "license": "Apache-2.0", "private": true, "scripts": {"build": "webpack"}, "dependencies": {"@material-ui/core": "^4.11.0", "@material-ui/icons": "^4.9.1", "@types/react-dom": "^17.0.1", "@types/react-router-dom": "^5.1.5", "bignumber.js": "^9.0.0", "filesize": "^6.1.0", "json-bigint": "^1.0.0", "react": "^17.0.1", "react-dom": "^17.0.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0"}, "devDependencies": {"html-webpack-plugin": "^5.1.0", "ts-loader": "^8.0.3", "typescript": "^4.0.2", "webpack": "^5.76.0", "webpack-cli": "^4.5.0"}}